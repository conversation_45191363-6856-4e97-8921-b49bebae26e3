@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");
:root {
  --primary-color: #111;
  --secondary-color: #4361ee;
  --accent-color: #3a56d4;
  --success-color: #4caf50;
  --error-color: #f44336;
  --warning-color: #ff9800;
  --light-bg: #f8f9fa;
  --white: #ffffff;
  --gray-100: #f8f9fa;
  --gray-200: #e9ecef;
  --gray-300: #dee2e6;
  --gray-400: #ced4da;
  --gray-500: #adb5bd;
  --gray-600: #6c757d;
  --gray-700: #495057;
  --gray-800: #343a40;
  --gray-900: #212529;
  --dark-bg: #2c2c2c;
  --red-primary: #ff4757;
  --blue-link: #6366f1;
  --border-radius: 12px;
  --box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Inter", "Helvetica Neue", Helvetica, Arial, sans-serif;
  background: #f5f5f5;
  min-height: 100vh;
  overflow: hidden;
}

main {
  display: flex;
  width: 100%;
  height: 100vh;
  align-items: center;
  justify-content: center;
}
main .login-container {
  border-radius: 1rem;
  display: flex;
  width: 80vw;
  height: 90vh;
  overflow: hidden;
  background: white;
}
main .left-section {
  flex: 1;
  background: var(--dark-bg);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 40px;
  position: relative;
  overflow: hidden;
}
main .left-section .brand-header h1 {
  color: white;
  font-size: 24px;
  font-weight: 600;
  letter-spacing: 1px;
}
main .left-section .shoe-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
main .left-section .shoe-container::before {
  content: "";
  position: absolute;
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, rgba(255, 77, 77, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
  border-radius: 50%;
  z-index: -1;
  animation: pulse 6s infinite alternate ease-in-out;
}
main .left-section .shoe-container::after {
  content: "";
  position: absolute;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(0, 0, 0, 0.05) 0%, rgba(255, 255, 255, 0) 70%);
  border-radius: 50%;
  z-index: -1;
  animation: pulse 8s infinite alternate-reverse ease-in-out;
}
main .left-section .shoe-container .animated-shoe {
  max-width: 100%;
  width: 350px;
  height: auto;
  transform: rotate(-15deg);
  transition: var(--transition);
  filter: drop-shadow(0 20px 30px rgba(0, 0, 0, 0.25));
  animation: float 6s infinite ease-in-out;
}
main .left-section .shoe-container .animated-shoe:hover {
  transform: rotate(-10deg) scale(1.05);
}
main .left-section .brand-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
main .left-section .brand-footer .brand-info h3 {
  color: white;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
}
main .left-section .brand-footer .brand-info p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}
main .left-section .brand-footer .limited-offer {
  display: flex;
  align-items: center;
}
main .left-section .brand-footer .limited-offer img {
  width: 6.2rem;
  height: 6rem;
  margin-right: 8px;
}
@keyframes float {
  0% {
    transform: rotate(-15deg) translateY(0px);
  }
  50% {
    transform: rotate(-12deg) translateY(-15px);
  }
  100% {
    transform: rotate(-15deg) translateY(0px);
  }
}
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 0.5;
  }
}
main .right-section {
  flex: 1;
  background: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 60px;
  position: relative;
  max-width: 500px;
}
main .right-section .language-selector {
  position: absolute;
  top: 30px;
  right: 30px;
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--gray-600);
  cursor: pointer;
}
main .right-section .language-selector span {
  font-size: 14px;
  font-weight: 500;
}
main .right-section .language-selector i {
  font-size: 12px;
}
main .right-section .language-selector:hover {
  color: var(--primary-color);
}
main .right-section .form-header {
  margin-bottom: 40px;
  text-align: left;
  margin-bottom: -0.2rem;
}
main .right-section .form-header h1 {
  font-size: 24px;
  font-weight: 700;
  color: var(--primary-color);
  margin-top: 16px;
  letter-spacing: 2px;
}
main .right-section .form-header h2 {
  font-size: 32px;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 8px;
  line-height: 1.2;
}
main .right-section .form-header p {
  color: var(--gray-600);
  font-size: 16px;
  line-height: 1.5;
  margin-top: -0.5rem;
  margin-bottom: 1rem;
}
main .form-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}
main .auth-form {
  display: none;
  animation: fadeIn 0.5s ease-in-out;
}
main .auth-form.active {
  display: block;
}
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
main .form-group {
  position: relative;
  margin-bottom: 24px;
  margin-top: -0.2rem;
}
main .form-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: 4px;
}
main .form-group input {
  width: 100%;
  padding: 16px;
  border: 1px solid var(--gray-300);
  border-radius: 8px;
  font-size: 16px;
  font-family: "Inter", sans-serif;
  transition: var(--transition);
  background: #f8f9fa;
}
main .form-group input::-moz-placeholder {
  color: var(--gray-500);
}
main .form-group input::placeholder {
  color: var(--gray-500);
}
main .form-group input:focus {
  outline: none;
  border-color: var(--secondary-color);
  background: white;
}
main .remember-field, main .terms-field {
  display: flex;
  align-items: center;
  margin-bottom: 18px;
  margin-top: -8px;
}
main .remember-field .checkbox-label, main .terms-field .checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 400;
  color: var(--gray-700);
  cursor: pointer;
}
main .remember-field .checkbox-label input[type=checkbox], main .terms-field .checkbox-label input[type=checkbox] {
  margin-right: 8px;
  accent-color: var(--red-primary);
  width: 16px;
  height: 16px;
  cursor: pointer;
}
main .remember-field .checkbox-label a, main .terms-field .checkbox-label a {
  color: var(--blue-link);
  text-decoration: underline;
  margin-left: 4px;
}
main .password-field .password-input-container {
  position: relative;
  display: flex;
  flex-direction: column;
  color: var(--error-color);
  align-items: left;
}
main .password-field input {
  width: 100%;
  padding: 16px 50px 16px 16px;
  border: 1px solid var(--gray-300);
  border-radius: 8px;
  font-size: 16px;
  font-family: "Inter", sans-serif;
  transition: var(--transition);
  background: #f8f9fa;
}
main .password-field input::-moz-placeholder {
  color: var(--gray-500);
}
main .password-field input::placeholder {
  color: var(--gray-500);
}
main .password-field input:focus {
  outline: none;
  border-color: var(--secondary-color);
  background: white;
}
main .password-field .password-toggle {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--gray-500);
  cursor: pointer;
  font-size: 18px;
  padding: 4px;
  transition: var(--transition);
  z-index: 10;
}
main .password-field .password-toggle:hover {
  color: var(--primary-color);
}
main .password-field .password-toggle i {
  display: block;
}
main .forgot-link {
  margin-bottom: 24px;
}
main .forgot-link a {
  color: var(--blue-link);
  text-decoration: none;
  font-size: 14px;
}
main .forgot-link a:hover {
  text-decoration: underline;
}
main .login-btn {
  width: 100%;
  padding: 16px;
  border: none;
  border-radius: 8px;
  background: var(--red-primary);
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  margin-bottom: 24px;
}
main .login-btn:hover {
  background: #ff3742;
  transform: translateY(-1px);
}
main .login-btn:active {
  transform: translateY(0);
}
main .signup-link {
  text-align: center;
  color: var(--gray-600);
  font-size: 14px;
  margin-top: -0.5rem;
}
main .signup-link a {
  color: var(--red-primary);
  text-decoration: none;
  font-weight: 500;
}
main .signup-link a:hover {
  text-decoration: underline;
}
@media (max-width: 768px) {
  main .login-container {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
  }
  main .left-section {
    flex: none;
    height: 40vh;
    padding: 20px;
  }
  main .left-section .shoe-container .animated-shoe {
    width: 200px;
  }
  main .left-section .brand-footer .brand-info h3 {
    font-size: 16px;
  }
  main .left-section .brand-footer .brand-info p {
    font-size: 12px;
  }
  main .left-section .brand-footer .social-icons .social-btn {
    width: 35px;
    height: 35px;
  }
  main .left-section .brand-footer .social-icons .social-btn i {
    font-size: 14px;
  }
  main .right-section {
    flex: none;
    padding: 20px;
  }
  main .right-section .form-header {
    margin-top: 20px;
    margin-bottom: 30px;
  }
  main .right-section .form-header h1 {
    font-size: 20px;
  }
  main .right-section .form-header h2 {
    font-size: 28px;
  }
  main .right-section .form-header p {
    font-size: 14px;
  }
}
@media (max-width: 480px) {
  main .left-section {
    height: 35vh;
    padding: 15px;
  }
  main .left-section .brand-header h1 {
    font-size: 20px;
  }
  main .left-section .shoe-container .animated-shoe {
    width: 150px;
  }
  main .right-section {
    padding: 15px;
  }
  main .right-section .form-header h1 {
    font-size: 18px;
  }
  main .right-section .form-header h2 {
    font-size: 24px;
  }
  main .form-group {
    margin-bottom: 20px;
  }
  main .form-group input {
    padding: 14px;
    font-size: 14px;
  }
  main .password-field input {
    padding: 14px 45px 14px 14px;
  }
  main .google-btn,
  main .login-btn {
    padding: 14px;
    font-size: 14px;
  }
}
main .loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  -webkit-backdrop-filter: blur(5px);
          backdrop-filter: blur(5px);
}
main .loading-overlay.active {
  display: flex;
}
main .loading-overlay .loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid var(--white);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}
main .loading-overlay p {
  color: var(--white);
  font-size: 1.1rem;
  font-weight: 500;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
main .notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  max-width: 400px;
}
main .notification {
  background: var(--white);
  border-radius: 8px;
  padding: 15px 20px;
  margin-bottom: 10px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
  border-left: 4px solid;
  display: flex;
  align-items: center;
  gap: 12px;
  animation: slideIn 0.3s ease-out;
  position: relative;
  overflow: hidden;
}
main .notification.success {
  border-left-color: var(--success-color);
}
main .notification.success .notification-icon {
  color: var(--success-color);
}
main .notification.error {
  border-left-color: var(--error-color);
}
main .notification.error .notification-icon {
  color: var(--error-color);
}
main .notification.warning {
  border-left-color: var(--warning-color);
}
main .notification.warning .notification-icon {
  color: var(--warning-color);
}
main .notification.info {
  border-left-color: var(--secondary-color);
}
main .notification.info .notification-icon {
  color: var(--secondary-color);
}
main .notification .notification-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}
main .notification .notification-content {
  flex: 1;
}
main .notification .notification-content .notification-title {
  font-weight: 600;
  color: var(--gray-800);
  margin-bottom: 2px;
}
main .notification .notification-content .notification-message {
  color: var(--gray-600);
  font-size: 0.9rem;
  line-height: 1.4;
}
main .notification .notification-close {
  background: none;
  border: none;
  color: var(--gray-500);
  cursor: pointer;
  font-size: 1.2rem;
  padding: 5px;
  transition: var(--transition);
}
main .notification .notification-close:hover {
  color: var(--gray-700);
}
main .notification .notification-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: currentColor;
  opacity: 0.3;
  animation: progress 5s linear forwards;
}
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
@keyframes progress {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}/*# sourceMappingURL=login.css.map */