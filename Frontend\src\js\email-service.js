/**
 * Email Service for StepStyle
 * Handles sending welcome emails and other notifications
 */

// EmailJS Configuration
const EMAILJS_CONFIG = {
  publicKey: "XPyoJ8UBgZ4vu3W7b",
  contactServiceId: "service_8gpdy3a",
  contactTemplateId: "template_pli8c3f",
  welcomeServiceId: "service_l5sqvpu", 
  welcomeTemplateId: "template_puwgwp7"
};

/**
 * Initialize EmailJS
 */
function initEmailJS() {
  if (typeof emailjs !== 'undefined') {
    emailjs.init(EMAILJS_CONFIG.publicKey);
    console.log('📧 EmailJS initialized successfully');
  } else {
    console.error('❌ EmailJS library not loaded');
  }
}

/**
 * Send welcome email to user after successful login/registration
 * @param {Object} userData - User data containing name and email
 * @param {string} userData.name - User's name
 * @param {string} userData.email - User's email
 * @param {string} type - Type of welcome ('login' or 'register')
 */
async function sendWelcomeEmail(userData, type = 'login') {
  try {
    console.log('📧 Sending welcome email to:', userData.email);
    
    if (typeof emailjs === 'undefined') {
      console.error('❌ EmailJS not available');
      return false;
    }

    const templateParams = {
      to_email: userData.email,
      to_name: userData.name,
      user_name: userData.name,
      user_email: userData.email,
      welcome_type: type,
      login_time: new Date().toLocaleString('en-IN', {
        timeZone: 'Asia/Kolkata',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }),
      website_url: window.location.origin
    };

    console.log('📧 Template params:', templateParams);

    const response = await emailjs.send(
      EMAILJS_CONFIG.welcomeServiceId,
      EMAILJS_CONFIG.welcomeTemplateId,
      templateParams
    );

    console.log('✅ Welcome email sent successfully:', response);
    return true;

  } catch (error) {
    console.error('❌ Failed to send welcome email:', error);
    return false;
  }
}

/**
 * Send contact form email (existing functionality)
 * @param {Object} contactData - Contact form data
 */
async function sendContactEmail(contactData) {
  try {
    console.log('📧 Sending contact email');
    
    if (typeof emailjs === 'undefined') {
      console.error('❌ EmailJS not available');
      return false;
    }

    const templateParams = {
      from_name: contactData.name,
      from_email: contactData.email,
      subject: contactData.subject,
      message: contactData.message,
      to_email: '<EMAIL>'
    };

    const response = await emailjs.send(
      EMAILJS_CONFIG.contactServiceId,
      EMAILJS_CONFIG.contactTemplateId,
      templateParams
    );

    console.log('✅ Contact email sent successfully:', response);
    return true;

  } catch (error) {
    console.error('❌ Failed to send contact email:', error);
    return false;
  }
}

/**
 * Show email notification to user
 * @param {string} message - Notification message
 * @param {string} type - Notification type ('success' or 'error')
 */
function showEmailNotification(message, type = 'success') {
  // Create notification element
  const notification = document.createElement('div');
  notification.className = `email-notification email-notification-${type}`;
  notification.innerHTML = `
    <div class="notification-icon">
      <i class="ph ph-${type === 'success' ? 'envelope-simple' : 'warning-circle'}"></i>
    </div>
    <div class="notification-message">${message}</div>
  `;
  
  // Add styles
  Object.assign(notification.style, {
    position: 'fixed',
    top: '20px',
    right: '20px',
    background: type === 'success' ? '#2ecc71' : '#e74c3c',
    color: 'white',
    padding: '12px 20px',
    borderRadius: '8px',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
    display: 'flex',
    alignItems: 'center',
    gap: '10px',
    zIndex: '10000',
    transform: 'translateX(100%)',
    transition: 'transform 0.3s ease',
    fontSize: '14px',
    fontFamily: 'Arial, sans-serif'
  });
  
  // Add to page
  document.body.appendChild(notification);
  
  // Animate in
  setTimeout(() => {
    notification.style.transform = 'translateX(0)';
  }, 100);
  
  // Remove after 4 seconds
  setTimeout(() => {
    notification.style.transform = 'translateX(100%)';
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }, 4000);
}

// Initialize EmailJS when DOM is loaded
document.addEventListener('DOMContentLoaded', initEmailJS);

// Export functions for use in other files
window.StepStyleEmailService = {
  sendWelcomeEmail,
  sendContactEmail,
  showEmailNotification,
  initEmailJS
};
