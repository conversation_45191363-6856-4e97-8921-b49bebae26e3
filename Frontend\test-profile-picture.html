<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile Picture Test - StepStyle</title>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/@phosphor-icons/web@2.1.1/src/regular/style.css" />
    <link rel="stylesheet" href="./src/styles/user-dropdown.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        .test-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #5a67d8;
        }
        .user-profile {
            position: relative !important;
            display: block !important;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Profile Picture Test</h1>
        
        <div class="test-section">
            <h3>Test Controls</h3>
            <button class="test-button" onclick="simulateLogin()">Simulate Login</button>
            <button class="test-button" onclick="simulateLogout()">Simulate Logout</button>
            <button class="test-button" onclick="testProfilePicture()">Test Profile Picture</button>
        </div>

        <div class="test-section">
            <h3>User Profile Dropdown</h3>
            <!-- User Profile (visible for testing) -->
            <div id="user-profile" class="user-profile" style="display: block;">
              <div class="user-icon-container">
                <div class="user-avatar">
                  <img class="user-profile-image" style="display: none;" alt="Profile">
                  <i class="ph ph-user user-icon"></i>
                  <div class="user-status-dot"></div>
                </div>
                <div class="user-dropdown">
                  <div class="dropdown-header">
                    <div class="user-avatar-large">
                      <img class="user-profile-image-large" style="display: none;" alt="Profile">
                      <i class="ph ph-user"></i>
                    </div>
                    <div class="user-info">
                      <div class="user-name">Test User</div>
                      <div class="user-email"><EMAIL></div>
                    </div>
                  </div>
                  <div class="dropdown-divider"></div>
                  <button class="profile-picture-btn dropdown-item">
                    <i class="ph ph-camera"></i>
                    <span>Change Picture</span>
                  </button>
                  <button class="remove-picture-btn dropdown-item" style="display: none;">
                    <i class="ph ph-trash"></i>
                    <span>Remove Picture</span>
                  </button>
                  <div class="dropdown-divider"></div>
                  <button class="logout-btn dropdown-item">
                    <i class="ph ph-sign-out"></i>
                    <span>Logout</span>
                  </button>
                </div>
              </div>
            </div>
        </div>

        <div class="test-section">
            <h3>Status</h3>
            <div id="status">Ready for testing...</div>
        </div>
    </div>

    <!-- Hidden file input for profile picture upload -->
    <input type="file" id="profile-picture-input" accept="image/*" style="display: none;">

    <script src="./src/js/profile-picture.js"></script>
    <script>
        // Test functions
        function simulateLogin() {
            const mockUser = {
                id: 'test-user-123',
                name: 'Test User',
                email: '<EMAIL>',
                profilePicture: ''
            };
            
            localStorage.setItem('stepstyle-token', 'local-token-test');
            localStorage.setItem('stepstyle-user', JSON.stringify(mockUser));
            
            updateStatus('User logged in successfully!');
            
            // Load profile picture
            if (window.profilePictureManager) {
                window.profilePictureManager.loadUserProfilePicture();
            }
        }

        function simulateLogout() {
            localStorage.removeItem('stepstyle-token');
            localStorage.removeItem('stepstyle-user');
            
            updateStatus('User logged out successfully!');
            
            // Hide profile picture
            if (window.profilePictureManager) {
                window.profilePictureManager.hideProfilePicture();
            }
        }

        function testProfilePicture() {
            // Create a test base64 image (colorful circular avatar)
            const testImage = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNmZjZkMDAiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSIxNiIgcj0iNiIgZmlsbD0id2hpdGUiLz4KPHBhdGggZD0iTTEwIDMwYzAtNS41IDQuNS0xMCAxMC0xMHMxMCA0LjUgMTAgMTAiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo=';

            if (window.profilePictureManager) {
                window.profilePictureManager.displayProfilePicture(testImage);
                updateStatus('Profile picture applied! Now exactly 40px x 40px (small) and 50px x 50px (large). Green dot on top!');
            } else {
                updateStatus('Profile picture manager not found!');
            }
        }

        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }

        // Initialize test environment
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('Test page loaded. Click buttons to test functionality.');
            
            // Make dropdown always visible for testing
            const dropdown = document.querySelector('.user-dropdown');
            if (dropdown) {
                dropdown.classList.add('active');
            }
        });

        // Simple notification function for testing
        function showNotification(message, type) {
            updateStatus(`${type.toUpperCase()}: ${message}`);
        }
    </script>
</body>
</html>
