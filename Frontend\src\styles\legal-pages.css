/* Legal Pages Styling */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

/* Navigation */
nav {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo h2 {
    color: #ff6b6b;
    font-size: 1.8rem;
    font-weight: 700;
    text-decoration: none;
}

.logo {
    text-decoration: none;
}

.back-home {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #666;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.back-home:hover {
    background-color: #f0f0f0;
    color: #ff6b6b;
}

/* Main Content */
.legal-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
    background: #fff;
    margin-top: 2rem;
    margin-bottom: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.container {
    width: 100%;
}

.page-header {
    text-align: center;
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 2px solid #f0f0f0;
}

.page-header h1 {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.last-updated {
    color: #666;
    font-size: 0.9rem;
    font-style: italic;
}

.content-section {
    margin-bottom: 2.5rem;
}

.content-section h2 {
    color: #ff6b6b;
    font-size: 1.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
    border-left: 4px solid #ff6b6b;
    padding-left: 1rem;
}

.content-section h3 {
    color: #333;
    font-size: 1.2rem;
    margin-bottom: 0.8rem;
    margin-top: 1.5rem;
    font-weight: 600;
}

.content-section h4 {
    color: #555;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.content-section p {
    margin-bottom: 1rem;
    color: #555;
    line-height: 1.7;
}

.content-section ul {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.content-section li {
    margin-bottom: 0.5rem;
    color: #555;
    line-height: 1.6;
}

/* Contact Info */
.contact-info {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    border-left: 4px solid #ff6b6b;
    margin-top: 1rem;
}

.contact-info p {
    margin-bottom: 0.5rem;
    color: #333;
}

/* Return Steps */
.return-steps {
    margin-top: 1.5rem;
}

.step {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #ff6b6b;
}

.step-number {
    background: #ff6b6b;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 1rem;
    flex-shrink: 0;
}

.step-content h4 {
    margin-bottom: 0.5rem;
    color: #333;
}

.step-content p {
    margin-bottom: 0;
    color: #666;
}

/* Timeline */
.timeline {
    margin-top: 1.5rem;
}

.timeline-item {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    position: relative;
}

.timeline-item:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 50px;
    top: 40px;
    width: 2px;
    height: 30px;
    background: #ddd;
}

.timeline-marker {
    background: #ff6b6b;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.9rem;
    margin-right: 1.5rem;
    min-width: 100px;
    text-align: center;
}

.timeline-content h4 {
    margin-bottom: 0.3rem;
    color: #333;
}

.timeline-content p {
    margin-bottom: 0;
    color: #666;
    font-size: 0.9rem;
}

/* Refund Methods */
.refund-methods {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.method {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.method:hover {
    border-color: #ff6b6b;
    transform: translateY(-2px);
}

.method i {
    font-size: 2.5rem;
    color: #ff6b6b;
    margin-bottom: 1rem;
}

.method h4 {
    margin-bottom: 0.5rem;
    color: #333;
}

.method p {
    margin-bottom: 0;
    color: #666;
    font-size: 0.9rem;
}

/* Footer */
.legal-footer {
    background: #333;
    color: #fff;
    padding: 2rem 0;
    margin-top: 3rem;
}

.legal-footer .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.legal-footer p {
    margin: 0;
    color: #ccc;
}

.footer-links {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.footer-links a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #ff6b6b;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-container {
        padding: 1rem;
    }

    .legal-content {
        margin: 1rem;
        padding: 1.5rem;
    }

    .page-header h1 {
        font-size: 2rem;
    }

    .content-section h2 {
        font-size: 1.3rem;
    }

    .step {
        flex-direction: column;
        text-align: center;
    }

    .step-number {
        margin-right: 0;
        margin-bottom: 1rem;
    }

    .timeline-item {
        flex-direction: column;
        text-align: center;
    }

    .timeline-item::after {
        display: none;
    }

    .timeline-marker {
        margin-right: 0;
        margin-bottom: 1rem;
    }

    .refund-methods {
        grid-template-columns: 1fr;
    }

    .legal-footer .container {
        flex-direction: column;
        text-align: center;
    }

    .footer-links {
        justify-content: center;
    }
}
