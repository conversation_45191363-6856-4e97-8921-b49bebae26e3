<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Login - StepStyle</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }
        
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo h1 {
            color: #333;
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }
        
        input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e1e1;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            width: 100%;
            padding: 12px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .message {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            display: none;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin-top: 20px;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .switch-form {
            text-align: center;
            margin-top: 20px;
        }
        
        .switch-form a {
            color: #667eea;
            text-decoration: none;
        }
        
        .switch-form a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <h1>StepStyle</h1>
            <p>Sign in to your account</p>
        </div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="email">Email Address</label>
                <input type="email" id="email" name="email" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" value="password123" required>
            </div>
            
            <button type="submit" class="btn" id="loginBtn">Sign In</button>
        </form>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Signing you in...</p>
        </div>
        
        <div class="message" id="message"></div>
        
        <div class="switch-form">
            <p>Don't have an account? <a href="#" onclick="switchToSignup()">Sign up</a></p>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:4000/api';
        
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            const loading = document.getElementById('loading');
            const message = document.getElementById('message');
            
            // Show loading
            loginBtn.disabled = true;
            loading.style.display = 'block';
            message.style.display = 'none';
            
            try {
                console.log('🧪 Attempting login with:', { email, password });
                
                const response = await fetch(`${API_BASE_URL}/user/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password }),
                });
                
                console.log('📡 Response status:', response.status);
                const data = await response.json();
                console.log('📝 Response data:', data);
                
                if (data.success) {
                    // Store authentication data
                    localStorage.setItem('stepstyle-token', data.token);
                    localStorage.setItem('stepstyle-user', JSON.stringify(data.user));
                    
                    // Show success message
                    message.className = 'message success';
                    message.textContent = 'Login successful! Redirecting...';
                    message.style.display = 'block';
                    
                    // Redirect after 2 seconds
                    setTimeout(() => {
                        window.location.href = './index.html';
                    }, 2000);
                    
                } else {
                    // Show error message
                    message.className = 'message error';
                    message.textContent = data.message || 'Login failed';
                    message.style.display = 'block';
                }
                
            } catch (error) {
                console.error('💥 Login error:', error);
                message.className = 'message error';
                message.textContent = 'Network error. Please check if the server is running.';
                message.style.display = 'block';
            } finally {
                // Hide loading
                loginBtn.disabled = false;
                loading.style.display = 'none';
            }
        });
        
        function switchToSignup() {
            alert('Signup functionality will be added here!');
        }
    </script>
</body>
</html>
