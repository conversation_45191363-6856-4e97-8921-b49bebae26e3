@charset "UTF-8";
/* Import base styles */
@import url("https://fonts.googleapis.com/css2?family=Fredoka:wght@400;500;600;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Bubblegum+Sans&display=swap");
@import 'remixicon/fonts/remixicon.css';
@font-face {
  font-family: "Gilroy-Regular";
  src: url("./assets/fonts/Gilroy-Regular.ttf");
}
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  width: 100%;
}

body {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

main {
  flex: 1;
  position: relative;
  width: 100%;
}
main header {
  height: 109vh;
  width: 100%;
  overflow: hidden;
}
main header #cursor {
  height: 2.8%;
  width: 1.5%;
  background-color: #0a0a0a;
  border-radius: 50%;
  position: fixed;
  z-index: 9;
  box-shadow: 0px 0px 10px 1px #000000;
  display: none;
}
main header nav {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background-color: transparent;
  -webkit-backdrop-filter: blur(3px);
          backdrop-filter: blur(3px);
  opacity: 1;
  position: fixed;
  top: 0;
  left: 0;
  transition: top 0.3s ease-in-out;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  z-index: 20;
}
main header nav #logo {
  height: 7%;
  width: 4%;
  margin-left: 1rem;
  margin-right: 10rem;
}
main header nav #logo img {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
main header nav #nav-middle {
  display: flex;
  align-items: center;
  gap: 2.5rem;
  justify-content: center;
  margin-right: -3rem;
}
main header nav #nav-middle a {
  text-decoration: none;
  font-family: funnel display medium;
  color: black;
}
main header nav #nav-middle a:hover {
  color: rgba(17, 17, 17, 0.5333333333);
}
main header nav #nav-last {
  display: flex;
  gap: 1rem;
  align-items: center;
  justify-content: center;
  margin-right: 3rem;
  flex-wrap: nowrap;
  transition: all 0.3s ease;
}
main header nav #nav-last #search-bar {
  display: flex;
  border-radius: 50px;
  border: 2px solid #111;
  width: 100%;
  transition: all 0.3s ease;
}
main header nav #nav-last #search-bar i {
  font-size: 1.3em;
}
main header nav #nav-last #search-bar .search-icon {
  margin-left: 0.2rem;
  margin-top: 0.3rem;
  border-radius: 50% 0 0 50%;
  background-color: transparent;
}
main header nav #nav-last #search-bar #nav-search {
  border: none;
  padding: 0.2rem;
  width: 12vw;
  border-radius: 0 50px 50px 0;
  font-size: 1rem;
  font-family: funnel display medium;
  outline: none;
  background-color: transparent;
  color: black;
  transition: width 0.3s ease;
}
main header nav #nav-last #search-bar #nav-search:active {
  background-color: transparent;
}
main header nav #nav-last #search-bar #nav-search::-webkit-search-cancel-button {
  cursor: pointer;
}
main header nav #nav-last.user-logged-in #search-bar {
  width: calc(100% + 120px);
}
main header nav #nav-last.user-logged-in #search-bar #nav-search {
  width: calc(12vw + 120px);
}
main header nav #nav-last.user-logged-out #search-bar {
  width: 100%;
}
main header nav #nav-last.user-logged-out #search-bar #nav-search {
  width: 12vw;
}
main header nav #nav-last .auth-buttons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: nowrap;
  margin-left: -2rem;
}
main header nav #nav-last .auth-buttons a {
  text-decoration: none;
  color: black;
  font-family: funnel display medium;
}
main header nav #nav-last .auth-buttons .login-btn {
  display: flex;
  background: linear-gradient(120deg, rgba(252, 206, 128, 0.8156862745), #fc9c79);
  flex-shrink: 0;
  flex-wrap: nowrap;
  padding: 0.6rem;
  border-radius: 0.4rem;
}
main header nav #nav-last .auth-buttons .signup-btn {
  display: flex;
  flex-shrink: 0;
  flex-wrap: nowrap;
  background: linear-gradient(120deg, #f97316, #ec4899);
  padding: 0.6rem;
  border-radius: 0.4rem;
}
main header nav #nav-last .user {
  font-size: 1rem;
}
main header nav #nav-last .user:hover {
  cursor: pointer;
}
main header #page1 {
  width: 100%;
  height: 150vh;
  z-index: 1;
  background-color: #fff;
}
main header #page1 #page1-part1 {
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translate(0, -1%);
  position: relative;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
main header #page1 #page1-part1 #bgcolor {
  height: 80%;
  width: 38%;
  background: linear-gradient(120deg, #fcce80, #FAA484);
  transform: translate(0, 10%);
  filter: blur(70px);
  border-radius: 50%;
  position: absolute;
}
main header #page1 #page1-part1 h1 {
  font-family: "Bebas Neue";
  color: #000;
  font-size: 20rem;
  font-weight: 100;
  text-transform: uppercase;
  margin-top: 10rem;
  margin-left: 2rem;
  z-index: 9;
}
main header #page1 #page1-part1 h1 span {
  display: inline-block;
}
main header #page1 #page1-part1 img {
  height: 70%;
  width: 50%;
  -o-object-fit: cover;
     object-fit: cover;
  position: absolute;
  transform: translate(-12%, -3%);
  z-index: 10;
  rotate: -30deg;
}
main #page2 {
  width: 100%;
  height: 100vh;
  position: relative;
  display: flex;
  flex-direction: row;
  background-color: #fff;
  gap: 1rem;
}
main #page2 .promo {
  display: flex;
  position: relative;
  width: 25%;
  height: 90%;
  gap: 0.5rem;
}
main #page2 .promo #developing {
  display: flex;
  align-items: center;
  justify-content: center;
  background-size: 2rem;
  width: 100%;
  height: 80%;
  transform: translate(0.1%, 19%);
  background-color: #fff;
}
main #page2 .promo img {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  position: absolute;
  z-index: 9;
}
main #page2 .promo #airmax {
  height: 80%;
  width: 70%;
  z-index: 9;
}
main #page2 .promo #AirMax1000 {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  z-index: 10;
}
main #page2 .promo .mouse-video {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
  opacity: 0;
  transform: scale(0.8);
  transition: transform 0.3s ease, opacity 0.3s ease;
  z-index: 10;
  /* Center the video over the image */
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.8);
}
main #page2 .promo .mouse-video video {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
main #page2 .promo .mouse-video.active {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}
main #page3 {
  width: 100%;
  height: 120vh;
  overflow-y: hidden;
  z-index: 1;
}
main #page3 .trending-part {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 2rem;
}
main #page3 .trending-part h1 {
  font-family: Helvetica now display medium;
  font-size: 1.5rem;
  font-weight: 570;
  margin-bottom: 1.5rem;
  letter-spacing: -0.9px;
  margin-left: 2rem;
}
main #page3 .mySwiper {
  width: 100%;
  height: 100%;
}
main #page3 .mySwiper .swiper-slide {
  text-align: center;
  margin-left: 2rem;
  font-size: 18px;
  background: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: left;
  width: 29%;
  height: 70%;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  gap: 1rem;
}
main #page3 .mySwiper .swiper-slide .card-upper {
  height: 97%;
  width: 90%;
}
main #page3 .mySwiper .swiper-slide .card-upper img {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
main #page3 .mySwiper .swiper-slide .card-lower {
  display: flex;
  align-items: left;
  text-align: left;
  flex-direction: column;
  margin-top: 1.3rem;
}
main #page3 .mySwiper .swiper-slide .card-lower h3 {
  font-family: helvetica now display medium;
  font-size: 1.1rem;
  font-weight: 100;
  letter-spacing: 0.01rem;
}
main #page3 .mySwiper .swiper-slide .card-lower .gender {
  font-size: 1.1rem;
  color: rgba(66, 66, 66, 0.87);
}
main #page3 .mySwiper .swiper-slide .card-lower .price {
  font-size: 1.1rem;
}
main #page3 .mySwiper .swiper-slide:hover {
  cursor: pointer;
}
main #page4 {
  height: 140vh;
  width: 100%;
  display: flex;
  flex-direction: column;
}
main #page4 h1 {
  font-family: Helvetica now display medium;
  font-size: 1.5rem;
  font-weight: 570;
  margin-bottom: 1.5rem;
  letter-spacing: -0.9px;
  margin-left: 2rem;
}
main #page4 .page4-grid {
  display: grid;
  margin-left: 1rem;
  height: 50%;
  grid-template-columns: repeat(4, 2fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 1rem;
  width: 95%;
  grid-template-areas: "a a a a" "b b c c";
}
main #page4 .page4-grid img {
  width: 100%;
  aspect-ratio: 3/1;
  -o-object-fit: cover;
     object-fit: cover;
}
main #page4 .page4-grid .image-a {
  grid-area: a;
  -o-object-position: 74% 74%;
     object-position: 74% 74%;
  height: 100%;
  position: relative;
}
main #page4 .page4-grid .image-a img {
  height: 100%;
  width: 100%;
  -o-object-position: 74% 74%;
     object-position: 74% 74%;
}
main #page4 .page4-grid .image-a #image-a-content {
  display: flex;
  flex-direction: column;
  transform: translate(2%, -37vw);
  font-family: Gilroy-Regular;
  color: #fff;
}
main #page4 .page4-grid .image-a #image-a-content h4 {
  font-weight: 100;
  font-size: 1rem;
  padding-left: 2rem;
}
main #page4 .page4-grid .image-a #image-a-content h1 {
  font-weight: 900;
  font-family: gilroy;
  letter-spacing: 0.1rem;
  font-size: 2.5rem;
  text-transform: uppercase;
}
main #page4 .page4-grid .image-a #image-a-content p {
  font-weight: 100;
  font-size: 1rem;
  padding-left: 2rem;
  width: 24%;
}
main #page4 .page4-grid .image-a #image-a-content button {
  width: 6%;
  padding: 0.5rem 1rem;
  margin-left: 2rem;
  margin-top: 1.5rem;
  border-radius: 0.2rem;
  border: none;
  background-color: #111;
  color: #fff;
  font-family: helvetica now display medium;
}
main #page4 .page4-grid .image-a #image-a-content button:hover {
  cursor: pointer;
}
main #page4 .page4-grid .image-b {
  grid-area: b;
  height: 100%;
}
main #page4 .page4-grid .image-b img {
  height: 100%;
  width: 100%;
}
main #page4 .page4-grid .image-b #image-b-content {
  display: flex;
  flex-direction: column;
  transform: translate(6%, -4.5vw);
  line-height: 1.5rem;
  color: #fff;
}
main #page4 .page4-grid .image-b #image-b-content h3 {
  font-family: gilroy-regular;
  font-weight: 600;
}
main #page4 .page4-grid .image-b:hover {
  cursor: pointer;
}
main #page4 .page4-grid .image-c {
  grid-area: c;
  height: 100%;
}
main #page4 .page4-grid .image-c img {
  height: 100%;
  width: 100%;
}
main #page4 .page4-grid .image-c #image-c-content {
  display: flex;
  flex-direction: column;
  transform: translate(5vh, -3vw);
  color: #fff;
}
main #page4 .page4-grid .image-c #image-c-content h3 {
  font-family: gilroy-regular;
  font-weight: 600;
}
main #page4 .page4-grid .image-c:hover {
  cursor: pointer;
}
main #page5 {
  width: 100%;
  height: 100vh;
}
main #page5 #page5-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-left: 2rem;
  font-family: gilroy-regular;
}
main #page5 #page5-bottom #page5-first-bottom {
  display: flex;
  flex-direction: column;
}
main #page5 #page5-bottom #page5-first-bottom h4 {
  font-family: helvetica now display medium;
  font-weight: 100;
  color: rgba(0, 0, 0, 0.862745098);
  font-size: 1.2rem;
}
main #page5 #page5-bottom #page5-first-bottom h1 {
  font-family: gilroy;
  font-size: 4rem;
  text-transform: uppercase;
  width: 80%;
}
main #page5 #page5-bottom #page5-first-bottom p {
  font-family: helvetica now display medium;
  width: 49%;
  color: rgba(0, 0, 0, 0.862745098);
}
main #page5 #page5-bottom #page5-first-bottom button {
  margin-top: 2rem;
  padding: 0.5rem 0.5rem;
  border: none;
  background-color: #111;
  color: #fff;
  font-family: helvetica now display medium;
  border-radius: 0.3rem;
  width: 16%;
}
main #page5 #page5-bottom #page5-first-bottom button:hover {
  cursor: not-allowed;
  background-color: rgba(0, 0, 0, 0.6705882353);
}
main #page5 #page5-bottom #page5-last-bottom #page5-image {
  display: flex;
  margin-right: 4rem;
  height: 100%;
  width: 100%;
}
main #page5 #page5-bottom #page5-last-bottom #page5-image img {
  height: 50%;
  width: 50%;
  -o-object-fit: cover;
     object-fit: cover;
}
main #page5 #page5-bottom #page5-last-bottom #page5-image #desgin {
  height: 50%;
  width: 20%;
  background-color: rgb(255, 255, 80);
  position: absolute;
}
main #page5 .modern-footer {
  background-color: #000000;
  color: #ffffff;
  padding: 60px 0 0;
  font-family: "Helvetica Now Display", Arial, sans-serif;
}
main #page5 .modern-footer .footer-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 0 20px 40px;
}
main #page5 .modern-footer .footer-container .footer-column {
  width: 22%;
  margin-bottom: 30px;
}
@media (max-width: 992px) {
  main #page5 .modern-footer .footer-container .footer-column {
    width: 45%;
  }
}
@media (max-width: 576px) {
  main #page5 .modern-footer .footer-container .footer-column {
    width: 100%;
  }
}
main #page5 .modern-footer .footer-container .footer-column h3 {
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  position: relative;
  padding-bottom: 10px;
}
main #page5 .modern-footer .footer-container .footer-column h3:after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 30px;
  height: 2px;
  background-color: #ffffff;
}
main #page5 .modern-footer .footer-container .footer-column ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
main #page5 .modern-footer .footer-container .footer-column ul li {
  margin-bottom: 10px;
}
main #page5 .modern-footer .footer-container .footer-column ul li a {
  color: #cccccc;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}
main #page5 .modern-footer .footer-container .footer-column ul li a:hover {
  color: #ffffff;
}
main #page5 .modern-footer .footer-container .footer-column .social-icons {
  display: flex;
  margin-bottom: 30px;
}
main #page5 .modern-footer .footer-container .footer-column .social-icons .social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background-color: #333333;
  border-radius: 50%;
  margin-right: 10px;
  color: #ffffff;
  transition: background-color 0.3s ease;
}
main #page5 .modern-footer .footer-container .footer-column .social-icons .social-icon:hover {
  background-color: #ff5a5f;
}
main #page5 .modern-footer .footer-container .footer-column .social-icons .social-icon i {
  font-size: 16px;
}
main #page5 .modern-footer .footer-container .footer-column .newsletter h4 {
  font-size: 16px;
  margin-bottom: 15px;
  font-weight: 400;
}
main #page5 .modern-footer .footer-container .footer-column .newsletter .newsletter-form {
  display: flex;
}
main #page5 .modern-footer .footer-container .footer-column .newsletter .newsletter-form input {
  flex: 1;
  background-color: #333333;
  border: none;
  padding: 10px 15px;
  color: #ffffff;
  font-size: 14px;
  border-radius: 4px 0 0 4px;
}
main #page5 .modern-footer .footer-container .footer-column .newsletter .newsletter-form input::-moz-placeholder {
  color: #999999;
}
main #page5 .modern-footer .footer-container .footer-column .newsletter .newsletter-form input::placeholder {
  color: #999999;
}
main #page5 .modern-footer .footer-container .footer-column .newsletter .newsletter-form button {
  background-color: #ff5a5f;
  color: #ffffff;
  border: none;
  padding: 10px 15px;
  font-size: 14px;
  cursor: pointer;
  border-radius: 0 4px 4px 0;
  transition: background-color 0.3s ease;
}
main #page5 .modern-footer .footer-container .footer-column .newsletter .newsletter-form button:hover {
  background-color: #ff3c41;
}
main #page5 .modern-footer .footer-bottom {
  border-top: 1px solid #333333;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  main #page5 .modern-footer .footer-bottom {
    flex-direction: column;
    text-align: center;
  }
}
main #page5 .modern-footer .footer-bottom .copyright {
  color: #999999;
  font-size: 14px;
  margin: 0;
}
@media (max-width: 768px) {
  main #page5 .modern-footer .footer-bottom .copyright {
    margin-bottom: 15px;
  }
}
main #page5 .modern-footer .footer-bottom .footer-links {
  display: flex;
}
@media (max-width: 576px) {
  main #page5 .modern-footer .footer-bottom .footer-links {
    flex-direction: column;
    align-items: center;
  }
}
main #page5 .modern-footer .footer-bottom .footer-links a {
  color: #999999;
  text-decoration: none;
  font-size: 14px;
  margin-left: 20px;
  transition: color 0.3s ease;
}
@media (max-width: 576px) {
  main #page5 .modern-footer .footer-bottom .footer-links a {
    margin: 5px 0;
  }
}
main #page5 .modern-footer .footer-bottom .footer-links a:hover {
  color: #ffffff;
}

.no-scroll {
  overflow: hidden;
}

main::-moz-selection {
  background-color: black;
  color: #fff;
}

main::selection {
  background-color: rgb(0, 0, 0);
  color: #fff;
}

.loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 100;
  background-color: #000000;
}

#loader {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  position: fixed;
  overflow: hidden;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #000000;
  z-index: 99;
}
#loader h3 {
  color: #fff;
  font-family: Bebas Neue;
  font-size: 8rem;
}
#loader img {
  height: 10%;
  width: 15%;
  transform: translate(15%, -3%);
  position: absolute;
  -o-object-fit: cover;
     object-fit: cover;
}

/* Cart Icon Container */
.cart-icon-container {
  position: relative;
  display: inline-block;
  cursor: pointer;
}
.cart-icon-container .cart-count {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #ff4d4d;
  color: white;
  font-size: 0.7rem;
  min-width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  transition: transform 0.3s ease;
  padding: 2px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  z-index: 10;
  font-family: "Helvetica Now Display", Arial, sans-serif;
}
.cart-icon-container.animate .cart-count {
  animation: pulse 0.5s ease;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.3);
  }
  100% {
    transform: scale(1);
  }
}
/* Cart Modal Styles */
.cart-modal {
  position: fixed;
  top: 0;
  right: -400px;
  width: 400px;
  height: 100%;
  background-color: white;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: right 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  overflow-y: auto;
}
.cart-modal::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: -1;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}
.cart-modal.open {
  right: 0;
}
.cart-modal.open::before {
  opacity: 1;
  visibility: visible;
}
.cart-modal .cart-modal-content {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 10px 0 0 10px;
}
.cart-modal .cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 15px;
  border-bottom: 2px solid #333;
  margin-bottom: 20px;
  position: relative;
}
.cart-modal .cart-header h3 {
  font-size: 1.5rem;
  margin: 0;
  font-family: "Helvetica Now Display", "Funnel Display Medium", sans-serif;
  font-weight: 600;
  position: relative;
  padding-left: 30px;
}
.cart-modal .cart-header h3::before {
  content: "🛒";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.2rem;
}
.cart-modal .cart-header .close-cart-btn {
  background: none;
  border: none;
  font-size: 1.8rem;
  cursor: pointer;
  color: #333;
  transition: all 0.3s ease;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.cart-modal .cart-header .close-cart-btn:hover {
  color: #ff4d4d;
  background-color: rgba(0, 0, 0, 0.05);
  transform: rotate(90deg);
}
.cart-modal .cart-items {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 20px;
}
.cart-modal .cart-items .cart-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #ddd;
  transition: all 0.3s ease;
}
.cart-modal .cart-items .cart-item:hover {
  background-color: rgba(0, 0, 0, 0.02);
}
.cart-modal .cart-items .cart-item .cart-item-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  margin-right: 15px;
  background-color: #f8f8f8;
  padding: 5px;
  border: 1px solid #ddd;
}
.cart-modal .cart-items .cart-item .cart-item-image img {
  width: 100%;
  height: 100%;
  -o-object-fit: contain;
     object-fit: contain;
  transition: transform 0.3s ease;
}
.cart-modal .cart-items .cart-item .cart-item-image:hover img {
  transform: scale(1.1);
}
.cart-modal .cart-items .cart-item .cart-item-details {
  flex: 1;
}
.cart-modal .cart-items .cart-item .cart-item-details .cart-item-name {
  font-weight: 600;
  margin-bottom: 5px;
  font-size: 1rem;
}
.cart-modal .cart-items .cart-item .cart-item-details .cart-item-price {
  color: #333;
  font-weight: 600;
  margin-bottom: 12px;
}
.cart-modal .cart-items .cart-item .cart-item-details .cart-item-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.cart-modal .cart-items .cart-item .cart-item-details .cart-item-controls .quantity-control {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 20px;
  padding: 2px;
  border: 1px solid #ddd;
}
.cart-modal .cart-items .cart-item .cart-item-details .cart-item-controls .quantity-control button {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: white;
  border: 1px solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
  font-weight: bold;
  transition: all 0.3s ease;
  cursor: pointer;
  color: #333;
}
.cart-modal .cart-items .cart-item .cart-item-details .cart-item-controls .quantity-control button:hover {
  background-color: #333;
  color: white;
  transform: scale(1.05);
}
.cart-modal .cart-items .cart-item .cart-item-details .cart-item-controls .quantity-control button:active {
  transform: scale(0.95);
}
.cart-modal .cart-items .cart-item .cart-item-details .cart-item-controls .quantity-control .quantity {
  font-weight: 600;
  width: 30px;
  text-align: center;
  font-size: 0.95rem;
}
.cart-modal .cart-items .cart-item .cart-item-details .cart-item-controls .remove-item {
  background-color: transparent;
  border: none;
  color: #666;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.3s ease;
  padding: 5px 10px;
  border-radius: 15px;
  border: 1px solid transparent;
}
.cart-modal .cart-items .cart-item .cart-item-details .cart-item-controls .remove-item:hover {
  color: #ff4d4d;
  border-color: #ff4d4d;
}
.cart-modal .cart-items .cart-item .cart-item-details .cart-item-controls .remove-item:active {
  transform: scale(0.95);
}
.cart-modal .cart-items .empty-cart-message {
  text-align: center;
  padding: 50px 0;
  color: #666;
  font-size: 1.1rem;
  font-family: "Funnel Display Medium", "Helvetica Now Display", sans-serif;
  position: relative;
}
.cart-modal .cart-items .empty-cart-message::before {
  content: "🛒";
  display: block;
  font-size: 3rem;
  margin-bottom: 15px;
  opacity: 0.5;
}
.cart-modal .cart-items .empty-cart-message .empty-cart-divider {
  margin: 20px auto;
  width: 50px;
  height: 2px;
  background-color: #ddd;
}
.cart-modal .cart-footer {
  padding-top: 15px;
  border-top: 2px solid #333;
  background-color: #f9f9f9;
  padding: 20px;
  border-radius: 0 0 10px 10px;
  box-shadow: 0 -5px 10px rgba(0, 0, 0, 0.05);
}
.cart-modal .cart-footer .cart-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  font-size: 1.2rem;
  font-weight: 600;
  font-family: "Helvetica Now Display", "Funnel Display Medium", sans-serif;
}
.cart-modal .cart-footer .cart-total span:first-child {
  color: #333;
}
.cart-modal .cart-footer .cart-total .total-amount {
  color: #333;
  font-size: 1.4rem;
  position: relative;
}
.cart-modal .cart-footer .cart-total .total-amount::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #ff4d4d;
  transform: scaleX(0);
  transition: transform 0.3s ease;
  transform-origin: right;
}
.cart-modal .cart-footer .cart-total .total-amount:hover::after {
  transform: scaleX(1);
  transform-origin: left;
}
.cart-modal .cart-footer .checkout-btn {
  width: 100%;
  padding: 15px;
  background-color: #333;
  color: white;
  border: none;
  border-radius: 30px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  letter-spacing: 0.5px;
  font-family: "Funnel Display Medium", "Helvetica Now Display", sans-serif;
}
.cart-modal .cart-footer .checkout-btn::after {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: -100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.5s ease;
}
.cart-modal .cart-footer .checkout-btn:hover {
  background-color: #ff4d4d;
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}
.cart-modal .cart-footer .checkout-btn:hover::after {
  left: 100%;
}
.cart-modal .cart-footer .checkout-btn:active {
  transform: translateY(0);
}

/* Toast Notification Styles */
.toast-notification {
  position: fixed;
  bottom: 30px;
  right: 30px;
  background-color: white;
  color: #333;
  padding: 15px 20px;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  z-index: 1001;
  transform: translateY(100px);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  font-family: "Helvetica Now Display", Arial, sans-serif;
}
.toast-notification.show {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}
.toast-notification.success {
  border-left: 4px solid #4CAF50;
}
.toast-notification.success .toast-icon {
  color: #4CAF50;
}
.toast-notification.error {
  border-left: 4px solid #F44336;
}
.toast-notification.error .toast-icon {
  color: #F44336;
}
.toast-notification .toast-content {
  display: flex;
  align-items: center;
}
.toast-notification .toast-content .toast-icon {
  font-size: 1.5rem;
  margin-right: 10px;
}
.toast-notification .toast-content .toast-message {
  font-size: 0.95rem;
  font-weight: 500;
}

/* Import Google Fonts */
/* Import Remix Icons */
/* Variables */
:root {
  --primary-color: #FF5757;
  --secondary-color: #4ECDC4;
  --accent-color: #FFD166;
  --purple-color: #9B5DE5;
  --pink-color: #F15BB5;
  --yellow-color: #FEE440;
  --blue-color: #00BBF9;
  --green-color: #00F5D4;
  --text-color: #333333;
  --light-gray: #f5f5f5;
  --medium-gray: #dddddd;
  --dark-gray: #999999;
  --white: #ffffff;
  --black: #000000;
  --border-radius: 15px;
  --box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

/* Base Styles */
body {
  font-family: "Fredoka", "Helvetica Now Display", sans-serif;
  background-color: var(--light-gray);
  color: var(--text-color);
  overflow-x: hidden;
}

h1, h2, h3, h4, h5, h6 {
  font-family: "Bubblegum Sans", "Fredoka", sans-serif;
  color: var(--text-color);
}

h2 {
  font-size: 2.5rem;
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
}
h2::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 5px;
  background: linear-gradient(to right, var(--pink-color), var(--blue-color));
  border-radius: 10px;
}

button {
  cursor: pointer;
  font-family: "Fredoka", sans-serif;
  font-weight: 600;
  transition: var(--transition);
}

/* Navbar Styles */
nav {
  background-color: var(--white);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}
nav #nav-middle a {
  font-weight: 500;
  position: relative;
}
nav #nav-middle a:hover {
  color: #acacac;
}
nav .cart-icon-container {
  position: relative;
  font-size: 1.5rem;
}
nav .cart-icon-container .cart-count {
  position: absolute;
  top: -12px;
  right: -8px;
  padding: 1px 6px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: var(--white);
  font-weight: bold;
  font-size: 0.8rem;
}
nav .cart-icon-container.animate .cart-count {
  animation: bounce 0.5s ease;
}

/* Hero Section */
#hero {
  position: relative;
  background: linear-gradient(135deg, var(--pink-color) 0%, var(--blue-color) 100%);
  padding: 4rem 2rem;
  overflow: hidden;
  margin-bottom: 3rem;
  border-radius: 0 0 30px 30px;
}
#hero .hero-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 2rem;
}
@media (max-width: 768px) {
  #hero .hero-content {
    flex-direction: column;
    text-align: center;
  }
}
#hero .hero-content .hero-text {
  flex: 1;
}
#hero .hero-content .hero-text h1 {
  font-size: 3.5rem;
  color: var(--white);
  margin-bottom: 1rem;
  text-shadow: 3px 3px 0 var(--purple-color);
}
@media (max-width: 768px) {
  #hero .hero-content .hero-text h1 {
    font-size: 2.5rem;
  }
}
#hero .hero-content .hero-text p {
  font-size: 1.4rem;
  color: var(--white);
  margin-bottom: 2rem;
  max-width: 500px;
}
@media (max-width: 768px) {
  #hero .hero-content .hero-text p {
    font-size: 1.2rem;
    margin-left: auto;
    margin-right: auto;
  }
}
#hero .hero-content .hero-image {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}
#hero .hero-content .hero-image img {
  max-width: 100%;
  height: auto;
  filter: drop-shadow(5px 5px 10px rgba(0, 0, 0, 0.3));
  transform: rotate(-5deg);
  transition: transform 0.5s ease;
}
#hero .hero-content .hero-image img:hover {
  transform: rotate(0deg) scale(1.05);
}
#hero .shop-now-btn {
  background-color: var(--yellow-color);
  color: var(--text-color);
  border: none;
  padding: 1rem 2.5rem;
  border-radius: 50px;
  font-size: 1.2rem;
  font-weight: 600;
  box-shadow: 0 5px 0 #ffc233;
  transition: all 0.2s ease;
}
#hero .shop-now-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 0 #ffc233;
}
#hero .shop-now-btn:active {
  transform: translateY(2px);
  box-shadow: 0 3px 0 #ffc233;
}
#hero .floating-shapes {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
#hero .floating-shapes .shape {
  position: absolute;
  border-radius: 50%;
}
#hero .floating-shapes .shape.shape-1 {
  width: 100px;
  height: 100px;
  background-color: rgba(255, 255, 255, 0.2);
  top: 20%;
  left: 10%;
  animation: float 8s ease-in-out infinite;
}
#hero .floating-shapes .shape.shape-2 {
  width: 60px;
  height: 60px;
  background-color: rgba(255, 255, 255, 0.15);
  top: 60%;
  left: 20%;
  animation: float 12s ease-in-out infinite;
}
#hero .floating-shapes .shape.shape-3 {
  width: 80px;
  height: 80px;
  background-color: rgba(255, 255, 255, 0.2);
  top: 30%;
  right: 20%;
  animation: float 10s ease-in-out infinite;
}
#hero .floating-shapes .shape.shape-4 {
  width: 50px;
  height: 50px;
  background-color: rgba(255, 255, 255, 0.15);
  bottom: 20%;
  right: 10%;
  animation: float 9s ease-in-out infinite;
}

/* Kids Collection Section */
#kids-collection {
  padding: 4rem 2rem;
}
#kids-collection .products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}
@media (max-width: 576px) {
  #kids-collection .products-grid {
    grid-template-columns: 1fr;
  }
}
#kids-collection .product-card {
  background: var(--white);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  cursor: pointer;
  transform: translateY(20px);
  opacity: 0;
}
#kids-collection .product-card.animate {
  animation: fadeInUp 0.6s ease forwards;
}
#kids-collection .product-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}
#kids-collection .product-card:hover .product-image img {
  transform: scale(1.1);
}
#kids-collection .product-card .product-image {
  position: relative;
  width: 100%;
  height: 220px;
  overflow: hidden;
}
#kids-collection .product-card .product-image img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  transition: transform 0.5s ease;
}
#kids-collection .product-card .product-image .product-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: var(--primary-color);
  color: var(--white);
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.8rem;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}
#kids-collection .product-card .product-info {
  padding: 1.5rem;
}
#kids-collection .product-card .product-info .product-name {
  font-size: 1.3rem;
  margin-bottom: 0.8rem;
  color: var(--text-color);
}
#kids-collection .product-card .product-info .product-price {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  margin-bottom: 1.2rem;
}
#kids-collection .product-card .product-info .product-price .original-price {
  font-size: 1rem;
  color: var(--dark-gray);
  text-decoration: line-through;
}
#kids-collection .product-card .product-info .product-price .current-price {
  font-size: 1.4rem;
  font-weight: 700;
  color: var(--primary-color);
}

/* Product Details Section */
#product-details {
  display: none;
  padding: 4rem 2rem;
  background-color: var(--white);
  border-radius: var(--border-radius);
  margin: 2rem auto;
  max-width: 1200px;
  box-shadow: var(--box-shadow);
}
#product-details.active {
  display: block;
  animation: fadeIn 0.5s ease;
}
#product-details .product-details-container {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
}
@media (max-width: 768px) {
  #product-details .product-details-container {
    flex-direction: column;
  }
}
#product-details .product-details-container .product-gallery {
  flex: 1;
  min-width: 300px;
  display: flex;
  gap: 1.5rem;
}
#product-details .product-details-container .product-gallery .thumbnails {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
#product-details .product-details-container .product-gallery .thumbnails .thumbnail {
  width: 80px;
  height: 80px;
  border-radius: 10px;
  -o-object-fit: cover;
     object-fit: cover;
  cursor: pointer;
  border: 2px solid transparent;
  transition: var(--transition);
}
#product-details .product-details-container .product-gallery .thumbnails .thumbnail:hover {
  transform: scale(1.05);
}
#product-details .product-details-container .product-gallery .thumbnails .thumbnail.active {
  border-color: var(--primary-color);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}
#product-details .product-details-container .product-gallery .main-image-container {
  flex: 1;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
}
#product-details .product-details-container .product-gallery .main-image-container img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  transition: transform 0.5s ease;
}
#product-details .product-details-container .product-gallery .main-image-container img:hover {
  transform: scale(1.03);
}
#product-details .product-details-container .product-info {
  flex: 1;
  min-width: 300px;
}
#product-details .product-details-container .product-info .product-title {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: var(--text-color);
}
#product-details .product-details-container .product-info .price-display {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}
#product-details .product-details-container .product-info .price-display .original-price {
  font-size: 1.2rem;
  color: var(--dark-gray);
  text-decoration: line-through;
}
#product-details .product-details-container .product-info .price-display .current-price {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
}
#product-details .product-details-container .product-info .price-display .discount-badge {
  background-color: var(--primary-color);
  color: var(--white);
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
}
#product-details .product-details-container .product-info .product-description {
  margin-bottom: 2rem;
}
#product-details .product-details-container .product-info .product-description h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
}
#product-details .product-details-container .product-info .product-description p {
  line-height: 1.6;
  color: var(--text-color);
}
#product-details .product-details-container .product-info .size-selector {
  margin-bottom: 2rem;
}
#product-details .product-details-container .product-info .size-selector h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
}
#product-details .product-details-container .product-info .size-selector .size-options {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}
#product-details .product-details-container .product-info .size-selector .size-options .size-btn {
  width: 60px;
  height: 60px;
  border-radius: 15px;
  border: 2px solid var(--medium-gray);
  background-color: var(--white);
  font-size: 1.1rem;
  font-weight: 600;
  transition: var(--transition);
}
#product-details .product-details-container .product-info .size-selector .size-options .size-btn:hover {
  border-color: var(--secondary-color);
  transform: translateY(-3px);
}
#product-details .product-details-container .product-info .size-selector .size-options .size-btn.active {
  background-color: var(--secondary-color);
  color: var(--white);
  border-color: var(--secondary-color);
}
#product-details .product-details-container .product-info .add-to-cart-btn {
  padding: 1rem 2rem;
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: 50px;
  font-size: 1.2rem;
  margin-right: 1rem;
  box-shadow: 0 5px 0 #ff2424;
  transition: all 0.2s ease;
}
#product-details .product-details-container .product-info .add-to-cart-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 0 #ff2424;
}
#product-details .product-details-container .product-info .add-to-cart-btn:active {
  transform: translateY(2px);
  box-shadow: 0 3px 0 #ff2424;
}
#product-details .product-details-container .product-info .back-to-collection-btn {
  padding: 1rem 2rem;
  background-color: var(--white);
  color: var(--secondary-color);
  border: 2px solid var(--secondary-color);
  border-radius: 50px;
  font-size: 1.2rem;
  transition: var(--transition);
}
#product-details .product-details-container .product-info .back-to-collection-btn:hover {
  background-color: var(--secondary-color);
  color: var(--white);
  transform: translateY(-3px);
}

/* Features Section */
#features {
  padding: 5rem 2rem;
  background-color: var(--light-gray);
  position: relative;
  overflow: hidden;
}
#features::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 87, 87, 0.05) 0%, rgba(0, 187, 249, 0.05) 100%);
  z-index: 0;
}
#features .features-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}
@media (max-width: 768px) {
  #features .features-container {
    grid-template-columns: 1fr;
  }
}
#features .features-container .feature {
  background-color: var(--white);
  border-radius: var(--border-radius);
  padding: 2rem;
  text-align: center;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
}
#features .features-container .feature:hover {
  transform: translateY(-15px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}
#features .features-container .feature i {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  display: inline-block;
}
#features .features-container .feature i.ri-shield-star-line {
  color: var(--primary-color);
}
#features .features-container .feature i.ri-footprint-line {
  color: var(--secondary-color);
}
#features .features-container .feature i.ri-paint-brush-line {
  color: var(--purple-color);
}
#features .features-container .feature i.ri-heart-line {
  color: var(--pink-color);
}
#features .features-container .feature h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
}
#features .features-container .feature p {
  color: var(--text-color);
  line-height: 1.6;
}

/* Reviews Section */
#reviews {
  padding: 5rem 2rem;
  background-color: var(--white);
}
#reviews .reviews-container {
  max-width: 1200px;
  margin: 0 auto;
}
#reviews .review-stats {
  text-align: center;
  margin-bottom: 3rem;
}
#reviews .review-stats .average-rating {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
}
#reviews .review-stats .average-rating .rating-number {
  font-size: 4rem;
  font-weight: 700;
  color: var(--primary-color);
  line-height: 1;
}
#reviews .review-stats .average-rating .stars {
  margin: 1rem 0;
}
#reviews .review-stats .average-rating .stars i {
  color: var(--yellow-color);
  font-size: 1.5rem;
  margin: 0 0.2rem;
}
#reviews .review-stats .average-rating .total-reviews {
  color: var(--text-color);
  opacity: 0.7;
}
#reviews .review-carousel {
  position: relative;
  height: 250px;
  margin-bottom: 2rem;
}
@media (max-width: 576px) {
  #reviews .review-carousel {
    height: 300px;
  }
}
#reviews .review-carousel .review-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  visibility: hidden;
  transition: all 0.5s ease;
}
#reviews .review-carousel .review-slide.active {
  opacity: 1;
  visibility: visible;
}
#reviews .review-carousel .review-content {
  background-color: var(--light-gray);
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--box-shadow);
  height: 100%;
}
#reviews .review-carousel .review-content .reviewer-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
}
#reviews .review-carousel .review-content .reviewer-info .reviewer-name {
  font-weight: 700;
  color: var(--text-color);
}
#reviews .review-carousel .review-content .reviewer-info .review-date {
  color: var(--dark-gray);
}
#reviews .review-carousel .review-content .stars {
  margin-bottom: 1rem;
}
#reviews .review-carousel .review-content .stars i {
  color: var(--yellow-color);
  font-size: 1.2rem;
  margin-right: 0.2rem;
}
#reviews .review-carousel .review-content .review-text {
  line-height: 1.6;
  color: var(--text-color);
}
#reviews .carousel-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
}
#reviews .carousel-controls .prev-btn, #reviews .carousel-controls .next-btn {
  background-color: var(--white);
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
}
#reviews .carousel-controls .prev-btn i, #reviews .carousel-controls .next-btn i {
  font-size: 1.5rem;
  color: var(--text-color);
}
#reviews .carousel-controls .prev-btn:hover, #reviews .carousel-controls .next-btn:hover {
  background-color: var(--secondary-color);
  transform: translateY(-3px);
}
#reviews .carousel-controls .prev-btn:hover i, #reviews .carousel-controls .next-btn:hover i {
  color: var(--white);
}
#reviews .carousel-controls .carousel-dots {
  display: flex;
  gap: 0.5rem;
}
#reviews .carousel-controls .carousel-dots .dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: var(--medium-gray);
  cursor: pointer;
  transition: var(--transition);
}
#reviews .carousel-controls .carousel-dots .dot.active {
  background-color: var(--primary-color);
  transform: scale(1.2);
}
#reviews .carousel-controls .carousel-dots .dot:hover {
  background-color: var(--primary-color);
}

/* Add Comment Section */
#add-comment {
  padding: 5rem 2rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}
#add-comment .comment-form-container {
  max-width: 600px;
  margin: 0 auto;
  background-color: var(--white);
  padding: 3rem;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}
#add-comment .comment-form-container h2 {
  text-align: center;
  color: var(--primary-color);
  margin-bottom: 1rem;
  font-size: 2.5rem;
  font-weight: 700;
}
#add-comment .comment-form-container p {
  text-align: center;
  color: var(--text-color);
  margin-bottom: 2.5rem;
  opacity: 0.8;
}
#add-comment .comment-form-container .comment-form .form-group {
  margin-bottom: 2rem;
}
#add-comment .comment-form-container .comment-form .form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text-color);
  font-size: 1.1rem;
}
#add-comment .comment-form-container .comment-form .form-group input[type=text], #add-comment .comment-form-container .comment-form .form-group textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: var(--transition);
  font-family: inherit;
}
#add-comment .comment-form-container .comment-form .form-group input[type=text]:focus, #add-comment .comment-form-container .comment-form .form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
}
#add-comment .comment-form-container .comment-form .form-group input[type=text]::-moz-placeholder, #add-comment .comment-form-container .comment-form .form-group textarea::-moz-placeholder {
  color: var(--medium-gray);
}
#add-comment .comment-form-container .comment-form .form-group input[type=text]::placeholder, #add-comment .comment-form-container .comment-form .form-group textarea::placeholder {
  color: var(--medium-gray);
}
#add-comment .comment-form-container .comment-form .form-group textarea {
  resize: vertical;
  min-height: 120px;
  line-height: 1.6;
}
#add-comment .comment-form-container .comment-form .rating-input {
  display: flex;
  align-items: center;
  gap: 1rem;
}
#add-comment .comment-form-container .comment-form .rating-input .stars-input {
  display: flex;
  gap: 0.3rem;
}
#add-comment .comment-form-container .comment-form .rating-input .stars-input i {
  font-size: 2rem;
  color: var(--medium-gray);
  cursor: pointer;
  transition: var(--transition);
}
#add-comment .comment-form-container .comment-form .rating-input .stars-input i:hover, #add-comment .comment-form-container .comment-form .rating-input .stars-input i.active {
  color: var(--yellow-color);
  transform: scale(1.1);
}
#add-comment .comment-form-container .comment-form .rating-input .rating-text {
  color: var(--text-color);
  font-size: 0.9rem;
  opacity: 0.7;
}
#add-comment .comment-form-container .comment-form .submit-comment-btn {
  width: 100%;
  background: linear-gradient(135deg, var(--primary-color) 0%, #ff8a80 100%);
  color: var(--white);
  border: none;
  padding: 1.2rem 2rem;
  border-radius: var(--border-radius);
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}
#add-comment .comment-form-container .comment-form .submit-comment-btn i {
  font-size: 1.2rem;
}
#add-comment .comment-form-container .comment-form .submit-comment-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
}
#add-comment .comment-form-container .comment-form .submit-comment-btn:active {
  transform: translateY(0);
}
#add-comment .comment-form-container .comment-form .submit-comment-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Comment Notification Styles */
.comment-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: var(--white);
  padding: 1rem 1.5rem;
  border-radius: var(--border-radius);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  gap: 0.8rem;
  z-index: 10000;
  transform: translateX(400px);
  opacity: 0;
  transition: all 0.3s ease;
  max-width: 350px;
}
.comment-notification.show {
  transform: translateX(0);
  opacity: 1;
}
.comment-notification.success {
  border-left: 4px solid #28a745;
}
.comment-notification.success i {
  color: #28a745;
  font-size: 1.2rem;
}
.comment-notification.error {
  border-left: 4px solid #dc3545;
}
.comment-notification.error i {
  color: #dc3545;
  font-size: 1.2rem;
}
.comment-notification span {
  color: var(--text-color);
  font-weight: 500;
  line-height: 1.4;
}

/* Cart Modal Styles */
.cart-modal {
  position: fixed;
  top: 0;
  right: -400px;
  width: 400px;
  height: 100vh;
  background-color: var(--white);
  box-shadow: -5px 0 20px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: right 0.4s ease;
  overflow-y: auto;
}
@media (max-width: 576px) {
  .cart-modal {
    width: 100%;
    right: -100%;
  }
}
.cart-modal.open {
  right: 0;
}
.cart-modal .cart-modal-content {
  padding: 2rem;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.cart-modal .cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}
.cart-modal .cart-header h3 {
  font-size: 1.5rem;
  color: var(--text-color);
}
.cart-modal .cart-header .close-cart-btn {
  background: none;
  border: none;
  font-size: 2rem;
  cursor: pointer;
  color: var(--text-color);
  transition: var(--transition);
}
.cart-modal .cart-header .close-cart-btn .close-cart-btn:hover {
  color: var(--primary-color);
  transform: rotate(90deg);
}
.cart-modal .cart-items {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 2rem;
}
.cart-modal .cart-items .empty-cart-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--dark-gray);
  text-align: center;
}
.cart-modal .cart-items .empty-cart-message .empty-cart-icon {
  width: 80px;
  height: 80px;
  margin-bottom: 1rem;
}
.cart-modal .cart-items .empty-cart-message p {
  font-size: 1.2rem;
  margin-bottom: 1rem;
}
.cart-modal .cart-items .empty-cart-message .empty-cart-divider {
  width: 50px;
  height: 3px;
  background-color: var(--medium-gray);
  border-radius: 3px;
}
.cart-modal .cart-items .cart-item {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--medium-gray);
}
.cart-modal .cart-items .cart-item .cart-item-image {
  width: 80px;
  height: 80px;
  border-radius: 10px;
  overflow: hidden;
}
.cart-modal .cart-items .cart-item .cart-item-image img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.cart-modal .cart-items .cart-item .cart-item-details {
  flex: 1;
}
.cart-modal .cart-items .cart-item .cart-item-details .cart-item-name {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-color);
}
.cart-modal .cart-items .cart-item .cart-item-details .cart-item-price {
  color: var(--primary-color);
  font-weight: 700;
  margin-bottom: 0.8rem;
}
.cart-modal .cart-items .cart-item .cart-item-details .cart-item-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.cart-modal .cart-items .cart-item .cart-item-details .cart-item-controls .quantity-control {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.cart-modal .cart-items .cart-item .cart-item-details .cart-item-controls .quantity-control button {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  border: none;
  padding: 2px 11px;
  background-color: var(--light-gray);
  color: var(--text-color);
  font-weight: 700;
  transition: var(--transition);
}
.cart-modal .cart-items .cart-item .cart-item-details .cart-item-controls .quantity-control button:hover {
  background-color: var(--secondary-color);
  color: var(--white);
}
.cart-modal .cart-items .cart-item .cart-item-details .cart-item-controls .quantity-control .quantity {
  font-weight: 600;
  color: var(--text-color);
}
.cart-modal .cart-items .cart-item .cart-item-details .cart-item-controls .remove-item {
  background: none;
  border: none;
  color: var(--dark-gray);
  cursor: pointer;
  transition: var(--transition);
}
.cart-modal .cart-items .cart-item .cart-item-details .cart-item-controls .remove-item:hover {
  color: var(--primary-color);
}
.cart-modal .cart-footer {
  border-top: 1px solid var(--medium-gray);
  padding-top: 1.5rem;
}
.cart-modal .cart-footer .cart-total {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--text-color);
}
.cart-modal .cart-footer .cart-total .total-amount {
  color: var(--primary-color);
}
.cart-modal .cart-footer .checkout-btn {
  width: 100%;
  padding: 1rem;
  border: none;
  background-color: var(--primary-color);
  color: var(--white);
  border-radius: 50px;
  font-size: 1.1rem;
  transition: var(--transition);
}
.cart-modal .cart-footer .checkout-btn:hover {
  background-color: #ff2424;
  transform: translateY(-3px);
}

/* Toast Notification */
.toast-notification {
  position: fixed;
  bottom: 30px;
  right: 30px;
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: 1rem 1.5rem;
  z-index: 1001;
  transform: translateY(100px);
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s ease;
}
.toast-notification.show {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}
.toast-notification.success {
  border-left: 4px solid var(--green-color);
}
.toast-notification.success .toast-icon {
  color: var(--green-color);
}
.toast-notification.error {
  border-left: 4px solid var(--primary-color);
}
.toast-notification.error .toast-icon {
  color: var(--primary-color);
}
.toast-notification .toast-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}
.toast-notification .toast-content .toast-icon {
  font-size: 1.5rem;
}
.toast-notification .toast-content .toast-message {
  font-weight: 600;
  color: var(--text-color);
}

/* Footer Styles */
footer.modern-footer {
  background-color: var(--black);
  color: var(--white);
  padding: 60px 0 0;
  font-family: "Helvetica Now Display", Arial, sans-serif;
}
footer.modern-footer .footer-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 0 20px 40px;
}
footer.modern-footer .footer-container .footer-column {
  width: 22%;
  margin-bottom: 30px;
}
@media (max-width: 992px) {
  footer.modern-footer .footer-container .footer-column {
    width: 45%;
  }
}
@media (max-width: 576px) {
  footer.modern-footer .footer-container .footer-column {
    width: 100%;
  }
}
footer.modern-footer .footer-container .footer-column h3 {
  color: var(--white);
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  position: relative;
  padding-bottom: 10px;
}
footer.modern-footer .footer-container .footer-column h3:after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 30px;
  height: 2px;
  background-color: var(--primary-color);
}
footer.modern-footer .footer-container .footer-column ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
footer.modern-footer .footer-container .footer-column ul li {
  margin-bottom: 10px;
}
footer.modern-footer .footer-container .footer-column ul li a {
  color: #cccccc;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}
footer.modern-footer .footer-container .footer-column ul li a:hover {
  color: var(--white);
}
footer.modern-footer .footer-container .footer-column.social .social-icons {
  display: flex;
  margin-bottom: 30px;
}
footer.modern-footer .footer-container .footer-column.social .social-icons a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background-color: #333333;
  border-radius: 50%;
  margin-right: 10px;
  color: var(--white);
  transition: background-color 0.3s ease;
}
footer.modern-footer .footer-container .footer-column.social .social-icons a:hover {
  background-color: var(--primary-color);
}
footer.modern-footer .footer-container .footer-column.social .social-icons a i {
  font-size: 16px;
}
footer.modern-footer .footer-container .footer-column.social .newsletter h4 {
  font-size: 16px;
  margin-bottom: 15px;
  font-weight: 400;
}
footer.modern-footer .footer-container .footer-column.social .newsletter .newsletter-form {
  display: flex;
}
footer.modern-footer .footer-container .footer-column.social .newsletter .newsletter-form input {
  flex: 1;
  background-color: #333333;
  border: none;
  padding: 10px 15px;
  color: var(--white);
  font-size: 14px;
  border-radius: 4px 0 0 4px;
}
footer.modern-footer .footer-container .footer-column.social .newsletter .newsletter-form input::-moz-placeholder {
  color: #999999;
}
footer.modern-footer .footer-container .footer-column.social .newsletter .newsletter-form input::placeholder {
  color: #999999;
}
footer.modern-footer .footer-container .footer-column.social .newsletter .newsletter-form button {
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
  padding: 10px 15px;
  font-size: 14px;
  cursor: pointer;
  border-radius: 0 4px 4px 0;
  transition: background-color 0.3s ease;
}
footer.modern-footer .footer-container .footer-column.social .newsletter .newsletter-form button:hover {
  background-color: #ff2424;
}
footer.modern-footer .footer-bottom {
  border-top: 1px solid #333333;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  footer.modern-footer .footer-bottom {
    flex-direction: column;
    text-align: center;
  }
}
footer.modern-footer .footer-bottom .copyright {
  color: #999999;
  font-size: 14px;
  margin: 0;
}
@media (max-width: 768px) {
  footer.modern-footer .footer-bottom .copyright {
    margin-bottom: 15px;
  }
}
footer.modern-footer .footer-bottom .footer-links {
  display: flex;
}
@media (max-width: 576px) {
  footer.modern-footer .footer-bottom .footer-links {
    flex-direction: column;
    align-items: center;
  }
}
footer.modern-footer .footer-bottom .footer-links a {
  color: #999999;
  text-decoration: none;
  font-size: 14px;
  margin-left: 20px;
  transition: color 0.3s ease;
}
@media (max-width: 576px) {
  footer.modern-footer .footer-bottom .footer-links a {
    margin: 5px 0;
  }
}
footer.modern-footer .footer-bottom .footer-links a:hover {
  color: var(--white);
}

/* Animations */
@keyframes float {
  0% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(10deg);
  }
  100% {
    transform: translateY(0) rotate(0deg);
  }
}
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes bounce {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}/*# sourceMappingURL=kids.css.map */